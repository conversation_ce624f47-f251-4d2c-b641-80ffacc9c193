<template>
  <div class="example-container">
    <h2>RegionSelect 组件示例 - ID 绑定模式</h2>
    
    <div class="example-section">
      <h3>基础用法（3级选择）</h3>
      <RegionSelect 
        v-model:value="basicValue" 
        :level="3"
        placeholder="请选择国家/省/市"
        @change="onBasicChange"
      />
      <div class="result">
        <p><strong>绑定值:</strong> {{ JSON.stringify(basicValue) }}</p>
        <p><strong>说明:</strong> 数组中每个元素对应一个层级的 ID</p>
        <ul v-if="basicValue.length">
          <li v-for="(id, index) in basicValue" :key="index">
            第{{ index + 1 }}级 ID: {{ id }}
          </li>
        </ul>
      </div>
    </div>

    <div class="example-section">
      <h3>4级选择（到区县）</h3>
      <RegionSelect 
        v-model:value="detailedValue" 
        :level="4"
        separator=" → "
        placeholder="请选择到区县级别"
        @change="onDetailedChange"
      />
      <div class="result">
        <p><strong>绑定值:</strong> {{ JSON.stringify(detailedValue) }}</p>
        <p><strong>层级说明:</strong></p>
        <ul v-if="detailedValue.length">
          <li>国家 ID: {{ detailedValue[0] || '未选择' }}</li>
          <li>省份 ID: {{ detailedValue[1] || '未选择' }}</li>
          <li>城市 ID: {{ detailedValue[2] || '未选择' }}</li>
          <li>区县 ID: {{ detailedValue[3] || '未选择' }}</li>
        </ul>
      </div>
    </div>

    <div class="example-section">
      <h3>2级选择（国家/省）</h3>
      <RegionSelect 
        v-model:value="simpleValue" 
        :level="2"
        placeholder="请选择国家和省份"
        @change="onSimpleChange"
      />
      <div class="result">
        <p><strong>绑定值:</strong> {{ JSON.stringify(simpleValue) }}</p>
        <p><strong>说明:</strong> 只选择到省级，返回 [国家ID, 省份ID]</p>
      </div>
    </div>

    <div class="example-section">
      <h3>预设值示例</h3>
      <button @click="setPresetValue" class="preset-btn">设置预设值</button>
      <button @click="clearValue" class="preset-btn">清空值</button>
      <RegionSelect 
        v-model:value="presetValue" 
        :level="3"
        placeholder="预设值示例"
        @change="onPresetChange"
      />
      <div class="result">
        <p><strong>绑定值:</strong> {{ JSON.stringify(presetValue) }}</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import RegionSelect from './RegionSelect.vue';

// 响应式数据
const basicValue = ref<number[]>([]);
const detailedValue = ref<number[]>([]);
const simpleValue = ref<number[]>([]);
const presetValue = ref<number[]>([]);

// 事件处理函数
const onBasicChange = (value: number[]) => {
  console.log('基础选择变化:', value);
};

const onDetailedChange = (value: number[]) => {
  console.log('详细选择变化:', value);
};

const onSimpleChange = (value: number[]) => {
  console.log('简单选择变化:', value);
};

const onPresetChange = (value: number[]) => {
  console.log('预设值变化:', value);
};

// 预设值操作
const setPresetValue = () => {
  // 示例：设置一个预设的 ID 数组
  presetValue.value = [1, 110000, 110100]; // 假设的 ID 值
};

const clearValue = () => {
  presetValue.value = [];
};
</script>

<style scoped>
.example-container {
  padding: 20px;
  max-width: 1000px;
  margin: 0 auto;
}

.example-section {
  margin-bottom: 40px;
  padding: 20px;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  background-color: #fafafa;
}

.example-section h3 {
  margin-top: 0;
  color: #1890ff;
  border-bottom: 2px solid #1890ff;
  padding-bottom: 8px;
}

.result {
  margin-top: 15px;
  padding: 15px;
  background-color: #f0f0f0;
  border-radius: 6px;
  border-left: 4px solid #52c41a;
}

.result p {
  margin: 8px 0;
  font-family: 'Courier New', monospace;
}

.result ul {
  margin: 10px 0;
  padding-left: 20px;
}

.result li {
  margin: 5px 0;
  color: #666;
}

.preset-btn {
  margin-right: 10px;
  margin-bottom: 15px;
  padding: 8px 16px;
  background-color: #1890ff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.preset-btn:hover {
  background-color: #40a9ff;
}

.preset-btn:active {
  background-color: #096dd9;
}
</style>
