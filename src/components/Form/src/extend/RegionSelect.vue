<template>
  <div style="width: 100%">
    <Cascader
      :value="cpval"
      :fieldNames="{
        label: 'name',
        value: 'name',
        children: 'children',
      }"
      :options="options"
      :loadData="loadData"
      :popupClassName="'vben-ant-cascader-menus'"
      style="width: 100%"
      @change="methods.onChange"
      :placeholder
    />
  </div>
</template>

<script lang="ts" setup>
  import { Cascader } from 'ant-design-vue';
  import { ref, computed, onMounted, unref, watch } from 'vue';
  import type { CascaderProps } from 'ant-design-vue';
  import { useRuleFormItem } from '@/hooks/component/useFormItem';
  import { findIndex, last, size, get, toNumber, isFunction } from 'lodash-es';
  import { useGlobSetting } from '@/hooks/setting';
  import { apiGetGlobalLocaltion } from '@/api/op/global';

  const globSetting = useGlobSetting();

  type FieldsType =
    | 'province'
    | 'city'
    | 'district'
    | 'street'
    | 'adcode'
    | 'citycode'
    | 'center'
    | 'name'
    | 'longitude'
    | 'latitude'
    | 'cityAdcode'
    | 'provinceAdcode'
    | 'districtAdcode';

  defineOptions({ name: 'RegionSelect' });
  type ValueType = number | string;
  type ValueState = ValueType[];
  interface Props {
    value?: ValueState;
    level?: number | string;
    separator?: string;
    // key 对应数组中的字段
    valueKey?: FieldsType[];
    placeholder?: string;
    afterFetch?: (...arg: any) => Promise<Recordable<any>>;
  }

  const props = withDefaults(defineProps<Props>(), {
    level: 3,
    value: () => [],
    separator: '/',
    valueKey: () => [
      'province',
      'city',
      'district',
      'street',
      'adcode',
      'citycode',
      'center',
      'name',
    ],
  });

  const options = ref<any[]>([]);
  const loading = ref<boolean>(true);
  let keyList: string[] = ['country', 'province', 'city', 'district', 'street'];

  defineEmits(['update:value', 'change']);

  // 计算属性 按照 valueKey 的顺序，将 value 数组转化成对象
  const formatValue = computed(() => {
    let obj: any = {};
    props.valueKey.forEach((key, index) => {
      if (state?.value?.[index]) {
        obj[key] = state?.value[index];
      }
    });
    return obj;
  });

  const cpval = computed(() => {
    // 按照 valueKey 的顺序，将 value 数组转化成对象
    if (!props.value) return [];
    const _v = unref(formatValue);
    const _address = [_v.province, _v.city, _v.district, _v.street];
    return _address.filter((item) => item);
  });

  const [state] = useRuleFormItem<Props, keyof Props, ValueState>(props, 'value', [
    'change',
    'update:value',
  ]);

  const loadData: CascaderProps['loadData'] = async (selectedOptions) => {
    const targetOption = selectedOptions[selectedOptions.length - 1];
    try {
      targetOption.loading = true;
      let res = await methods.reqAmapLocation(targetOption.adcode);
      if (size(res)) {
        targetOption.children = res;
      } else {
        targetOption.children = [];
        targetOption.isLeaf = true;
      }
      targetOption.loading = false;
    } catch (error) {
      targetOption.loading = false;
    }
  };

  const methods = {
    // 初始化
    async init() {
      try {
        loading.value = true;
        let res = await methods.reqAmapLocation(100000);
        options.value = res;
      } catch (error) {
        console.log(error);
      } finally {
        loading.value = false;
      }
    },
    async reqAmapLocation(adcode) {
      try {
        const url = `https://restapi.amap.com/v3/config/district?key=${globSetting.appMapWebKey}&keywords=${adcode}&subdistrict=2`;
        const response = await fetch(url, { method: 'GET' });
        const { districts = [] } = await response.json();
        if (districts.length > 0) {
          let district = districts[0];
          let dis = district.districts;
          let l = dis.map((item: any) => {
            return {
              name: item.name,
              adcode: item.adcode,
              citycode: size(item.citycode) ? item.citycode : undefined,
              level: item.level,
              isLeaf: methods.showLeaf(item.districts),
              center: item.center,
              longitude: methods.splitString(item.center, 0),
              latitude: methods.splitString(item.center, 1),
              ...splitAdcode(item.adcode),
            };
          });

          const { afterFetch } = props;

          if (afterFetch && isFunction(afterFetch)) {
            l = (await afterFetch(l)) || [];
          }

          return Promise.resolve(l);
        }
      } catch (error) {
        return Promise.reject(error);
      }
    },

    displayRender({ labels }) {
      return labels.join(props.separator);
    },
    onChange(value: any, selectedOptions) {
      if (!selectedOptions) {
        selectedOptions = [];
      }
      // 从选择的数据中构建
      let _opt: any = {};

      selectedOptions.forEach((item) => {
        _opt[item.level] = item.name;
      });
      // 将数组中的 name 拼接起来
      let _name = selectedOptions.map((item) => {
        return item.name;
      });
      _opt.name = _name.join(props.separator);

      // 获取最后一个选中参数
      let lastData = last(selectedOptions);
      // 获取 adcode
      _opt.adcode = get(lastData, 'adcode');
      _opt.center = get(lastData, 'center');
      _opt.citycode = get(lastData, 'citycode');
      _opt.longitude = get(lastData, 'longitude');
      _opt.latitude = get(lastData, 'latitude');
      _opt.provinceAdcode = get(lastData, 'provinceAdcode');
      _opt.cityAdcode = get(lastData, 'cityAdcode');
      _opt.districtAdcode = get(lastData, 'districtAdcode');

      // 更新 state 按照 valueKey 构建
      const valueKeys = props.valueKey.map((key) => {
        return _opt[key];
      });
      state.value = valueKeys;
    },
    // 判断是否需要展示出来
    showLeaf(districts) {
      if (size(districts)) {
        // 存在子数据
        // 查询子数据是否符合规则
        const hasMatchingChild = districts.some(({ level }) => {
          // 替换此处逻辑为你的具体匹配规则
          return methods.checkCondition(level);
        });
        return hasMatchingChild;
      } else {
        return true;
      }
    },
    checkCondition(level) {
      const currLevel = findIndex(keyList, (item) => {
        return item === level;
      });
      let isLeaf = toNumber(props.level) + 1 <= currLevel;
      return isLeaf;
    },
    // 拼接字符串拆分
    splitString(str: string, index: number, separator: string = ',') {
      if (str) {
        return str.split(separator)[index] || '';
      }
      return '';
    },
  };

  function splitAdcode(adcode: string) {
    if (adcode.length !== 6) {
      throw new Error('Invalid adcode length. Adcode must be 6 digits.');
    }

    return {
      provinceAdcode: adcode.slice(0, 2), // 前两位表示省级代码
      cityAdcode: adcode.slice(2, 4), // 中间两位表示地级代码
      districtAdCode: adcode.slice(4, 6), // 后两位表示县级代码
    };
  }

  onMounted(() => {
    methods.init();
  });

  // 监听 level 的变化
  watch(
    () => props.level,
    (newLevel, oldLevel) => {
      if (newLevel !== oldLevel) {
        methods.init();
      }
    },
  );
</script>

<style lang="less">
  .vben-ant-cascader-menus {
    .ant-cascader-menu {
      height: 300px !important;
    }
  }
</style>
