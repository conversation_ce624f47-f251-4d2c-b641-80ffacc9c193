<template>
  <div style="width: 100%">
    <Cascader
      :value="cpval"
      :fieldNames="{
        label: 'name',
        value: 'name',
        children: 'children',
      }"
      :options="options"
      :loadData="loadData"
      :popupClassName="'vben-ant-cascader-menus'"
      style="width: 100%"
      @change="methods.onChange"
      :placeholder
    />
  </div>
</template>

<script lang="ts" setup>
  import { Cascader } from 'ant-design-vue';
  import { ref, computed, onMounted, unref, watch } from 'vue';
  import type { CascaderProps } from 'ant-design-vue';
  import { useRuleFormItem } from '@/hooks/component/useFormItem';
  import { last, size, get, toNumber, isFunction } from 'lodash-es';
  import { apiGetGlobalLocaltion } from '@/api/op/global';
  import type { GlobalLocaltion } from '@/api/op/model/global';

  type FieldsType =
    | 'province'
    | 'city'
    | 'district'
    | 'street'
    | 'id'
    | 'parentId'
    | 'level'
    | 'code'
    | 'chineseName'
    | 'foreignName'
    | 'pathChinese'
    | 'pathForeign'
    | 'name';

  defineOptions({ name: 'RegionSelect' });
  type ValueType = number | string;
  type ValueState = ValueType[];
  interface Props {
    value?: ValueState;
    level?: number | string;
    separator?: string;
    // key 对应数组中的字段
    valueKey?: FieldsType[];
    placeholder?: string;
    afterFetch?: (...arg: any) => Promise<Recordable<any>>;
  }

  const props = withDefaults(defineProps<Props>(), {
    level: 3,
    value: () => [],
    separator: '/',
    valueKey: () => ['province', 'city', 'district', 'street', 'id', 'code', 'chineseName', 'name'],
  });

  const options = ref<any[]>([]);
  const loading = ref<boolean>(true);

  defineEmits(['update:value', 'change']);

  // 计算属性 按照 valueKey 的顺序，将 value 数组转化成对象
  const formatValue = computed(() => {
    let obj: any = {};
    props.valueKey.forEach((key, index) => {
      if (state?.value?.[index]) {
        obj[key] = state?.value[index];
      }
    });
    return obj;
  });

  const cpval = computed(() => {
    // 按照 valueKey 的顺序，将 value 数组转化成对象
    if (!props.value) return [];
    const _v = unref(formatValue);
    const _address = [_v.province, _v.city, _v.district, _v.street];
    return _address.filter((item) => item);
  });

  const [state] = useRuleFormItem<Props, keyof Props, ValueState>(props, 'value', [
    'change',
    'update:value',
  ]);

  const loadData: CascaderProps['loadData'] = async (selectedOptions) => {
    const targetOption = selectedOptions[selectedOptions.length - 1];
    try {
      targetOption.loading = true;
      let res = await methods.reqGlobalLocation(targetOption.id);
      if (size(res)) {
        targetOption.children = res;
      } else {
        targetOption.children = [];
        targetOption.isLeaf = true;
      }
      targetOption.loading = false;
    } catch (error) {
      targetOption.loading = false;
    }
  };

  const methods = {
    // 初始化 - 获取根级数据（国家级，parentId为0或null）
    async init() {
      try {
        loading.value = true;
        let res = await methods.reqGlobalLocation(0);
        options.value = res;
      } catch (error) {
        console.log(error);
      } finally {
        loading.value = false;
      }
    },
    // 使用新的全球位置接口获取数据
    async reqGlobalLocation(pid: number) {
      try {
        const res = await apiGetGlobalLocaltion(pid);
        // 处理返回的数据，可能是单个对象或数组
        const dataArray = Array.isArray(res) ? res : [res];
        let l = dataArray.map((item: GlobalLocaltion) => {
          return {
            id: item.id,
            name: item.chineseName || item.foreignName,
            chineseName: item.chineseName,
            foreignName: item.foreignName,
            code: item.code,
            level: item.level,
            parentId: item.parentId,
            pathChinese: item.pathChinese,
            pathForeign: item.pathForeign,
            isLeaf: methods.checkCondition(item.level),
          };
        });

        const { afterFetch } = props;

        if (afterFetch && isFunction(afterFetch)) {
          const result = await afterFetch(l);
          l = Array.isArray(result) ? result : l;
        }

        return Promise.resolve(l);
      } catch (error) {
        return Promise.reject(error);
      }
    },

    displayRender({ labels }) {
      return labels.join(props.separator);
    },
    onChange(value: any, selectedOptions: any) {
      if (!selectedOptions) {
        selectedOptions = [];
      }
      // 从选择的数据中构建
      let _opt: any = {};

      // 根据层级构建地区信息
      selectedOptions.forEach((item: any, index: number) => {
        const levelNames = ['country', 'province', 'city', 'district', 'street'];
        if (levelNames[index]) {
          _opt[levelNames[index]] = item.name;
        }
      });

      // 将数组中的 name 拼接起来
      let _name = selectedOptions.map((item: any) => {
        return item.name;
      });
      _opt.name = _name.join(props.separator);

      // 获取最后一个选中参数
      let lastData = last(selectedOptions);
      if (lastData) {
        _opt.id = get(lastData, 'id');
        _opt.code = get(lastData, 'code');
        _opt.level = get(lastData, 'level');
        _opt.parentId = get(lastData, 'parentId');
        _opt.chineseName = get(lastData, 'chineseName');
        _opt.foreignName = get(lastData, 'foreignName');
        _opt.pathChinese = get(lastData, 'pathChinese');
        _opt.pathForeign = get(lastData, 'pathForeign');
      }

      // 更新 state 按照 valueKey 构建
      const valueKeys = props.valueKey.map((key) => {
        return _opt[key];
      });
      state.value = valueKeys;
    },

    checkCondition(level: number) {
      // level 是数字类型，直接比较
      // 如果当前层级大于等于设定的最大层级，则为叶子节点
      let isLeaf = level >= toNumber(props.level);
      return isLeaf;
    },
    // 拼接字符串拆分
    splitString(str: string, index: number, separator: string = ',') {
      if (str) {
        return str.split(separator)[index] || '';
      }
      return '';
    },
  };

  onMounted(() => {
    methods.init();
  });

  // 监听 level 的变化
  watch(
    () => props.level,
    (newLevel, oldLevel) => {
      if (newLevel !== oldLevel) {
        methods.init();
      }
    },
  );
</script>

<style lang="less">
  .vben-ant-cascader-menus {
    .ant-cascader-menu {
      height: 300px !important;
    }
  }
</style>
