# RegionSelect 组件字段说明

## GlobalLocaltion 接口字段

基于 `src/api/op/model/global.ts` 中的 `GlobalLocaltion` 接口，组件支持以下字段：

### 基础字段
- `id: number` - 主键
- `parentId: number` - 父类id
- `level: number` - 等级
- `path: string` - 路径
- `code: string` - 代码

### 国际化字段
- `languageCode: string` - 语言编码
- `chineseName: string` - 中文名称
- `foreignName: string` - 外文名称
- `pathChinese: string` - 路径中文名
- `pathForeign: string` - 路径外语名
- `pinyin: string` - 拼音

### 国家相关字段
- `countryNumber: string` - 国家区号
- `phoneNumber: string` - 国家电话区号
- `abbreviation: string` - 国家缩写
- `iso: string` - 时区

### 组件内部字段
- `name: string` - 显示名称（由 `chineseName || foreignName` 生成）

## valueKey 配置示例

### 默认配置
```typescript
valueKey: ['chineseName', 'code', 'id', 'level', 'pathChinese', 'name']
```

### 常用配置示例

#### 1. 基础信息
```typescript
valueKey: ['chineseName', 'code', 'id']
```

#### 2. 完整路径信息
```typescript
valueKey: ['pathChinese', 'pathForeign', 'code', 'level']
```

#### 3. 国际化信息
```typescript
valueKey: ['chineseName', 'foreignName', 'languageCode', 'pinyin']
```

#### 4. 国家相关信息
```typescript
valueKey: ['chineseName', 'countryNumber', 'phoneNumber', 'abbreviation', 'iso']
```

## 使用示例

```vue
<template>
  <RegionSelect 
    v-model:value="regionValue" 
    :level="3"
    :valueKey="['chineseName', 'code', 'id', 'level']"
    separator=" > "
    placeholder="请选择地区"
    @change="onRegionChange"
  />
</template>

<script setup>
import { ref } from 'vue';

const regionValue = ref([]);

const onRegionChange = (value) => {
  console.log('选中的值:', value);
  // value 数组按照 valueKey 的顺序包含对应的字段值
  // 例如: ['北京市', '110000', 1, 2]
};
</script>
```

## 注意事项

1. **字段类型**: 所有字段都基于 `GlobalLocaltion` 接口定义
2. **显示名称**: `name` 字段是组件内部生成的，优先使用 `chineseName`，如果为空则使用 `foreignName`
3. **层级控制**: `level` 字段是数字类型，用于控制级联选择器的层级深度
4. **路径信息**: `pathChinese` 和 `pathForeign` 包含完整的层级路径信息
5. **国际化支持**: 组件同时支持中文和外文名称显示
