# RegionSelect 组件字段说明

## GlobalLocaltion 接口字段

基于 `src/api/op/model/global.ts` 中的 `GlobalLocaltion` 接口，组件支持以下字段：

### 基础字段

- `id: number` - 主键
- `parentId: number` - 父类id
- `level: number` - 等级
- `path: string` - 路径
- `code: string` - 代码

### 国际化字段

- `languageCode: string` - 语言编码
- `chineseName: string` - 中文名称
- `foreignName: string` - 外文名称
- `pathChinese: string` - 路径中文名
- `pathForeign: string` - 路径外语名
- `pinyin: string` - 拼音

### 国家相关字段

- `countryNumber: string` - 国家区号
- `phoneNumber: string` - 国家电话区号
- `abbreviation: string` - 国家缩写
- `iso: string` - 时区

### 组件内部字段

- `name: string` - 显示名称（由 `chineseName || foreignName` 生成）

## valueKey 配置说明

### 默认配置（所有层级都使用 id）

```typescript
valueKey: ['id', 'id', 'id', 'id', 'id', 'id']; // 所有层级都使用 id 作为绑定值
```

### 绑定逻辑

- **国家层级**: 绑定国家的 `id`
- **省份层级**: 绑定省份的 `id`
- **城市层级**: 绑定城市的 `id`
- **区县层级**: 绑定区县的 `id`
- **街道层级**: 绑定街道的 `id`

### 返回值格式

组件返回的 `value` 是一个数组，包含每个层级选中项的 `id` 值：

```typescript
// 示例：选择了 中国 > 北京市 > 朝阳区
value: [1, 110000, 110105]; // [国家id, 省份id, 区县id]
```

## 使用示例

```vue
<template>
  <RegionSelect
    v-model:value="regionValue"
    :level="3"
    separator=" > "
    placeholder="请选择地区"
    @change="onRegionChange"
  />
</template>

<script setup>
  import { ref } from 'vue';

  const regionValue = ref([]);

  const onRegionChange = (value) => {
    console.log('选中的值:', value);
    // value 数组包含每个层级选中项的 id 值
    // 例如: [1, 110000, 110105] // [国家id, 省份id, 区县id]
  };
</script>
```

## 注意事项

1. **字段类型**: 所有字段都基于 `GlobalLocaltion` 接口定义
2. **显示名称**: `name` 字段是组件内部生成的，优先使用 `chineseName`，如果为空则使用 `foreignName`
3. **层级控制**: `level` 字段是数字类型，用于控制级联选择器的层级深度
4. **路径信息**: `pathChinese` 和 `pathForeign` 包含完整的层级路径信息
5. **国际化支持**: 组件同时支持中文和外文名称显示
