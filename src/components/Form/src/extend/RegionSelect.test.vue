<template>
  <div class="test-container">
    <h2>RegionSelect 组件测试</h2>

    <div class="test-section">
      <h3>基本用法</h3>
      <RegionSelect
        v-model:value="basicValue"
        :level="3"
        placeholder="请选择地区"
        @change="onBasicChange"
      />
      <p>选中值: {{ JSON.stringify(basicValue) }}</p>
    </div>

    <div class="test-section">
      <h3>4级选择（到区县）</h3>
      <RegionSelect
        v-model:value="customValue"
        :level="4"
        separator=" > "
        placeholder="请选择地区（4级选择）"
        @change="onCustomChange"
      />
      <p>选中值: {{ JSON.stringify(customValue) }}</p>
      <p>说明: 返回数组包含每个层级的 id 值</p>
    </div>

    <div class="test-section">
      <h3>带后处理函数</h3>
      <RegionSelect
        v-model:value="processedValue"
        :level="2"
        :afterFetch="afterFetchHandler"
        placeholder="请选择地区（带后处理）"
        @change="onProcessedChange"
      />
      <p>选中值: {{ JSON.stringify(processedValue) }}</p>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue';
  import RegionSelect from './RegionSelect.vue';

  const basicValue = ref([]);
  const customValue = ref([]);
  const processedValue = ref([]);

  const onBasicChange = (value: any) => {
    console.log('基本用法变化:', value);
  };

  const onCustomChange = (value: any) => {
    console.log('自定义字段变化:', value);
  };

  const onProcessedChange = (value: any) => {
    console.log('后处理变化:', value);
  };

  // 后处理函数示例
  const afterFetchHandler = async (data: any[]) => {
    console.log('原始数据:', data);
    // 可以在这里对数据进行过滤、排序等处理
    return data.filter((item) => item.chineseName); // 只显示有中文名的项目
  };
</script>

<style scoped>
  .test-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
  }

  .test-section {
    margin-bottom: 30px;
    padding: 20px;
    border: 1px solid #e8e8e8;
    border-radius: 6px;
  }

  .test-section h3 {
    margin-top: 0;
    color: #1890ff;
  }

  .test-section p {
    margin-top: 10px;
    padding: 10px;
    border-radius: 4px;
    background-color: #f5f5f5;
    font-family: monospace;
  }
</style>
