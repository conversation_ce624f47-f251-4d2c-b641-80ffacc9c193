import { BasicColumn, FormSchema } from '@/components/Table';

export const basicColumns: BasicColumn[] = [
  {
    title: '合作伙伴简称',
    dataIndex: 'companyShort',
  },
  {
    title: '地区',
    dataIndex: 'country',
  },
  {
    title: '联系人',
    dataIndex: 'linkman',
  },
  {
    title: '联系电话',
    dataIndex: 'mobile',
  },
  {
    title: '平台登录账号',
    dataIndex: 'username',
  },
  {
    title: '账号状态',
    dataIndex: 'state',
  },
  {
    title: '关联设备数量',
    dataIndex: 'deviceCount',
  },
];

export const searchSchema: FormSchema[] = [
  {
    field: 'name',
    label: '伙伴名称',
    component: 'Input',
  },
  {
    // TODO: 暂时用input代替
    field: 'province',
    label: '省',
    component: 'Input',
  },
];
